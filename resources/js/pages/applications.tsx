import { PlaceholderPattern } from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Applications',
        href: '/applications',
    },
];

export default function Applications() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Applications" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Applications</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                        Manage and monitor your applications
                    </p>
                </div>
                
                <div className="grid auto-rows-min gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div className="relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                        <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
                        <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-center">
                                <h3 className="font-medium text-gray-900 dark:text-gray-100">App 1</h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Status: Active</p>
                            </div>
                        </div>
                    </div>
                    
                    <div className="relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                        <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
                        <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-center">
                                <h3 className="font-medium text-gray-900 dark:text-gray-100">App 2</h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Status: Inactive</p>
                            </div>
                        </div>
                    </div>
                    
                    <div className="relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                        <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
                        <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-center">
                                <h3 className="font-medium text-gray-900 dark:text-gray-100">App 3</h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Status: Pending</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div className="relative min-h-[50vh] flex-1 overflow-hidden rounded-xl border border-sidebar-border/70 md:min-h-min dark:border-sidebar-border">
                    <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
                    <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Details</h3>
                            <p className="text-gray-500 dark:text-gray-400">
                                Detailed application information and metrics will be displayed here
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
